#!/usr/bin/env python3
"""
Test only config and basic imports without pygame.
"""

import sys
import os

# Add the project root to the path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

def test_config():
    """Test config imports"""
    print("Testing config...")

    try:
        from config import ENVIRONMENTAL_HAZARDS, SPECIAL_FEATURES
        print("✅ Config imported successfully")

        print(f"\nEnvironmental Hazards ({len(ENVIRONMENTAL_HAZARDS)}):")
        for hazard_name, config in ENVIRONMENTAL_HAZARDS.items():
            damage = config.get('damage', 0)
            print(f"  {hazard_name}: {damage} damage")

        print(f"\nSpecial Features ({len(SPECIAL_FEATURES)}):")
        for feature_name, config in SPECIAL_FEATURES.items():
            print(f"  {feature_name}: {len(config)} properties")

        print("\nBiome system removed - using normal level generation instead")

        return True

    except Exception as e:
        print(f"❌ Config test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_terrain_types():
    """Test terrain types - DISABLED (terrain system removed)"""
    print("\nTesting terrain types...")
    print("⚠️  Terrain system removed - using normal tile sprites instead")
    print("✅ Using enhanced wall.png and floor.png textures")
    return True

def main():
    """Run config-only tests"""
    print("Config-Only Integration Test")
    print("=" * 40)

    success = True

    if not test_config():
        success = False

    if not test_terrain_types():
        success = False

    print("\n" + "=" * 40)
    if success:
        print("✅ Config tests passed!")
    else:
        print("❌ Config tests failed.")

    return 0 if success else 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
